#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速车牌识别脚本 - 针对固定位置优化
使用区域裁剪来快速识别车牌、车型和接单次数
"""

import os
import re
import csv
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from paddleocr import PaddleOCR


class FastLicensePlateOCR:
    def __init__(self):
        """初始化PaddleOCR"""
        print("正在初始化PaddleOCR...")
        try:
            self.ocr = PaddleOCR(lang='ch')
            print("PaddleOCR初始化成功！")
        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            raise
    
    def get_crop_regions(self) -> List[Tuple]:
        """
        返回需要裁剪的固定区域

        Returns:
            裁剪区域列表，每个元素为(x1, y1, x2, y2, region_name)
        """
        # 图片尺寸是高度2400，宽度1080，根据顺风车界面布局调整坐标
        # 车牌信息通常在屏幕下方
        return [
            (50, 1800, 1000, 1950, "车牌区域"),      # 车牌号码和颜色区域
            (50, 1950, 1000, 2100, "车型区域"),      # 车型区域
            (600, 1950, 1050, 2100, "接单次数区域")  # 接单次数区域（右侧）
        ]
    
    def extract_text_from_region(self, image_path: str) -> Dict[str, List]:
        """
        从图片的固定区域提取文本
        
        Args:
            image_path: 图片路径
            
        Returns:
            按区域分组的识别结果
        """
        try:
            print(f"  正在处理: {Path(image_path).name}")
            
            # 读取图片
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                print("  无法解码图片")
                return {}
            
            print(f"  图片尺寸: {img.shape}")
            
            # 获取裁剪区域
            crop_regions = self.get_crop_regions()
            region_results = {}
            
            for x1, y1, x2, y2, region_name in crop_regions:
                print(f"    识别区域: {region_name} ({x1},{y1},{x2},{y2})")
                
                # 裁剪图片
                cropped_img = img[y1:y2, x1:x2]
                
                if cropped_img.size == 0:
                    print(f"    区域 {region_name} 裁剪失败")
                    region_results[region_name] = []
                    continue
                
                # OCR识别
                result = self.ocr.ocr(cropped_img)
                
                texts = []
                if result and result[0]:
                    # 处理新版本OCRResult格式
                    first_result = result[0]
                    if hasattr(first_result, 'rec_texts'):
                        # 新版本格式
                        for text, score in zip(first_result.rec_texts, first_result.rec_scores):
                            texts.append((text, score))
                            print(f"      {region_name}: '{text}' (置信度: {score:.3f})")
                    else:
                        # 传统格式
                        for item in result[0]:
                            if len(item) == 2:
                                bbox, (text, confidence) = item
                                texts.append((text, confidence))
                                print(f"      {region_name}: '{text}' (置信度: {confidence:.3f})")
                
                region_results[region_name] = texts
            
            return region_results
            
        except Exception as e:
            print(f"  处理失败: {e}")
            return {}
    
    def extract_license_plate(self, texts: List[Tuple]) -> Optional[Dict]:
        """从文本中提取车牌信息"""
        license_patterns = [
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}',
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]',
        ]
        
        color_keywords = ['蓝', '黄', '白', '黑', '绿', '红', '灰']
        
        for text, confidence in texts:
            if confidence < 0.5:
                continue
                
            for pattern in license_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    license_plate = matches[0]
                    
                    # 查找颜色
                    color = None
                    for color_word in color_keywords:
                        if color_word in text:
                            color = color_word
                            break
                    
                    return {
                        'license_plate': license_plate,
                        'color': color,
                        'full_text': text,
                        'confidence': confidence
                    }
        
        return None
    
    def extract_vehicle_model(self, texts: List[Tuple]) -> Optional[str]:
        """从文本中提取车型信息"""
        vehicle_keywords = [
            '轿车', 'SUV', 'MPV', '面包车', '货车', '客车', '卡车',
            '奔驰', '宝马', '奥迪', '大众', '丰田', '本田', '日产',
            '比亚迪', '吉利', '长安', '哈弗', '红旗', '蔚来', '理想',
            '小鹏', '特斯拉', '广汽', '埃安', '零跑', '深蓝', '乐道'
        ]
        
        for text, confidence in texts:
            if confidence < 0.6:
                continue
                
            for keyword in vehicle_keywords:
                if keyword in text:
                    return text.strip()
        
        return None
    
    def extract_order_count(self, texts: List[Tuple]) -> Optional[int]:
        """从文本中提取接单次数"""
        order_patterns = [
            r'(\d+)\s*次',
            r'(\d+)\s*单',
            r'接单\s*(\d+)',
            r'(\d+)\s*接单'
        ]
        
        for text, confidence in texts:
            if confidence < 0.6:
                continue
                
            for pattern in order_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    try:
                        return int(matches[0])
                    except ValueError:
                        continue
        
        # 查找纯数字
        for text, confidence in texts:
            if confidence < 0.6:
                continue
            numbers = re.findall(r'\d+', text)
            for num in numbers:
                if len(num) <= 4:  # 接单次数通常不会太大
                    try:
                        return int(num)
                    except ValueError:
                        continue
        
        return None
    
    def process_image(self, image_path: str) -> Dict:
        """处理单张图片"""
        # 提取各区域文本
        region_results = self.extract_text_from_region(image_path)
        
        # 从车牌区域提取车牌信息
        license_plate_info = None
        if "车牌区域" in region_results:
            license_plate_info = self.extract_license_plate(region_results["车牌区域"])
        
        # 从车型区域提取车型信息
        vehicle_model = None
        if "车型区域" in region_results:
            vehicle_model = self.extract_vehicle_model(region_results["车型区域"])
        
        # 从接单次数区域提取接单次数
        order_count = None
        if "接单次数区域" in region_results:
            order_count = self.extract_order_count(region_results["接单次数区域"])
        
        # 收集所有文本
        all_text = []
        for region_name, texts in region_results.items():
            for text, confidence in texts:
                all_text.append((f"[{region_name}]{text}", confidence))
        
        return {
            'image_path': image_path,
            'license_plate_info': license_plate_info,
            'vehicle_model': vehicle_model,
            'order_count': order_count,
            'all_text': all_text,
            'region_results': region_results
        }
    
    def process_directory(self, data_dir: str = 'data') -> List[Dict]:
        """处理目录下的所有图片"""
        results = []
        data_path = Path(data_dir)
        
        if not data_path.exists():
            print(f"数据目录不存在: {data_dir}")
            return results
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        for image_file in data_path.rglob('*'):
            if image_file.suffix.lower() in image_extensions:
                result = self.process_image(str(image_file))
                results.append(result)
        
        return results
    
    def save_results_csv(self, results: List[Dict], output_file: str = 'fast_ocr_results.csv'):
        """保存结果到CSV文件"""
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            headers = [
                '图片路径', '文件名', '城市', '人员', '平台',
                '车牌号码', '车牌颜色', '车型', '接单次数', 
                '车牌区域文本', '车型区域文本', '接单次数区域文本'
            ]
            writer.writerow(headers)
            
            for result in results:
                path_parts = Path(result['image_path']).parts
                filename = Path(result['image_path']).name
                
                city = path_parts[1] if len(path_parts) >= 3 and path_parts[1] != 'data' else ''
                person = path_parts[3] if len(path_parts) >= 5 else ''
                platform = path_parts[4] if len(path_parts) >= 6 else ''
                
                license_plate = ''
                license_color = ''
                if result['license_plate_info']:
                    license_plate = result['license_plate_info']['license_plate']
                    license_color = result['license_plate_info']['color'] or ''
                
                vehicle_model = result['vehicle_model'] or ''
                order_count = result['order_count'] if result['order_count'] is not None else ''
                
                # 各区域的文本
                license_region_text = '; '.join([f"{text}({conf:.2f})" for text, conf in result['region_results'].get('车牌区域', [])])
                vehicle_region_text = '; '.join([f"{text}({conf:.2f})" for text, conf in result['region_results'].get('车型区域', [])])
                order_region_text = '; '.join([f"{text}({conf:.2f})" for text, conf in result['region_results'].get('接单次数区域', [])])
                
                row = [
                    result['image_path'], filename, city, person, platform,
                    license_plate, license_color, vehicle_model, order_count,
                    license_region_text, vehicle_region_text, order_region_text
                ]
                writer.writerow(row)
        
        print(f"CSV结果已保存到: {output_file}")


def main():
    """主函数"""
    print("开始快速车牌识别...")
    
    # 初始化OCR
    ocr_processor = FastLicensePlateOCR()
    
    # 处理所有图片
    results = ocr_processor.process_directory('data')
    
    # 保存结果
    ocr_processor.save_results_csv(results)
    
    print(f"处理完成！共处理 {len(results)} 张图片")


if __name__ == "__main__":
    main()
