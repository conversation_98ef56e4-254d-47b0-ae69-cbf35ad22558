#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别脚本
使用PaddleOCR识别图片中的车牌号码、颜色、车型和接单次数
"""

import os
import re
import json
import csv
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont


class LicensePlateOCR:
    def __init__(self, use_angle_cls=True, lang='ch'):
        """
        初始化PaddleOCR

        Args:
            use_angle_cls: 是否使用角度分类器
            lang: 语言，默认中文
        """
        print("正在初始化PaddleOCR...")
        try:
            # 使用新的参数名称
            self.ocr = PaddleOCR(
                use_textline_orientation=use_angle_cls,
                lang=lang,
                text_det_thresh=0.3,  # 降低检测阈值
                text_det_box_thresh=0.5,  # 降低框选阈值
                text_recognition_batch_size=6  # 批处理数量
            )
            print("PaddleOCR初始化成功！")
        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            # 如果新参数失败，尝试使用基本参数
            try:
                print("尝试使用基本参数初始化...")
                self.ocr = PaddleOCR(lang=lang)
                print("PaddleOCR基本初始化成功！")
            except Exception as e2:
                print(f"基本初始化也失败: {e2}")
                raise
        
    def extract_text_from_image(self, image_path: str, crop_regions: List[Tuple] = None) -> List[Tuple]:
        """
        从图片中提取文本，支持区域裁剪

        Args:
            image_path: 图片路径
            crop_regions: 裁剪区域列表，每个元素为(x1, y1, x2, y2, region_name)

        Returns:
            包含文本信息的列表，每个元素为(坐标, 文本, 置信度)
        """
        try:
            print(f"  正在OCR识别: {Path(image_path).name}")

            # 检查文件是否存在
            if not os.path.exists(image_path):
                print(f"  文件不存在: {image_path}")
                return []

            # 检查文件大小
            file_size = os.path.getsize(image_path)
            print(f"  文件大小: {file_size} bytes")

            # 使用cv2读取图片来避免中文路径问题
            import cv2
            import numpy as np

            # 读取图片数据
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 解码图片
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                print("  无法解码图片")
                return []

            # 如果指定了裁剪区域，只识别这些区域
            if crop_regions:
                all_results = []
                for x1, y1, x2, y2, region_name in crop_regions:
                    print(f"    识别区域: {region_name} ({x1},{y1},{x2},{y2})")

                    # 裁剪图片
                    cropped_img = img[y1:y2, x1:x2]

                    if cropped_img.size == 0:
                        print(f"    区域 {region_name} 裁剪失败")
                        continue

                    # 对裁剪区域进行OCR
                    result = self.ocr.ocr(cropped_img)

                    if result and result[0]:
                        # 调整坐标到原图坐标系
                        adjusted_results = []
                        for item in result[0]:
                            if len(item) == 2:
                                bbox, text_info = item
                                if isinstance(text_info, tuple) and len(text_info) == 2:
                                    text, confidence = text_info
                                    # 调整bbox坐标
                                    adjusted_bbox = []
                                    for point in bbox:
                                        adjusted_bbox.append([point[0] + x1, point[1] + y1])
                                    adjusted_results.append((adjusted_bbox, (f"[{region_name}]{text}", confidence)))
                        all_results.extend(adjusted_results)

                return all_results
            else:
                # 使用numpy数组进行OCR识别
                result = self.ocr.ocr(img)

            if result is None:
                print("  OCR返回None")
                return []

            # 检查返回结果的格式
            print(f"  OCR结果类型: {type(result)}")
            print(f"  OCR结果长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")

            # 打印前几个元素来了解结构
            if isinstance(result, list) and len(result) > 0:
                print(f"  第一个元素类型: {type(result[0])}")
                if hasattr(result[0], '__len__'):
                    print(f"  第一个元素长度: {len(result[0])}")
                print(f"  第一个元素内容: {result[0]}")

            # 新版本PaddleOCR可能返回字典格式
            if isinstance(result, dict):
                # 提取文本识别结果
                if 'rec_texts' in result and 'rec_scores' in result and 'rec_polys' in result:
                    texts = result['rec_texts']
                    scores = result['rec_scores']
                    polys = result['rec_polys']

                    if len(texts) == len(scores) == len(polys):
                        print(f"  识别到 {len(texts)} 个文本区域")

                        # 构造标准格式的结果
                        ocr_results = []
                        for i, (text, score, poly) in enumerate(zip(texts, scores, polys)):
                            print(f"    文本{i+1}: '{text}' (置信度: {score:.3f})")
                            ocr_results.append((poly, (text, score)))

                        return ocr_results
                    else:
                        print("  文本、分数、多边形数量不匹配")
                        return []
                else:
                    print("  字典格式中缺少必要的键")
                    return []

            # 处理新版本PaddleOCR的列表格式（包含OCRResult对象）
            elif isinstance(result, list):
                if not result:
                    print("  OCR返回空列表")
                    return []

                # 检查第一个元素是否是OCRResult对象
                first_result = result[0]

                # 尝试多种方式访问OCRResult对象的数据
                try:
                    # 方式1：直接访问属性
                    texts = first_result.rec_texts
                    scores = first_result.rec_scores
                    polys = first_result.rec_polys

                    if len(texts) == len(scores) == len(polys):
                        print(f"  识别到 {len(texts)} 个文本区域")

                        # 构造标准格式的结果
                        ocr_results = []
                        for i, (text, score, poly) in enumerate(zip(texts, scores, polys)):
                            print(f"    文本{i+1}: '{text}' (置信度: {score:.3f})")
                            ocr_results.append((poly, (text, score)))

                        return ocr_results
                    else:
                        print("  文本、分数、多边形数量不匹配")
                        return []

                except AttributeError:
                    # 方式2：尝试字典访问
                    try:
                        texts = first_result['rec_texts']
                        scores = first_result['rec_scores']
                        polys = first_result['rec_polys']

                        if len(texts) == len(scores) == len(polys):
                            print(f"  识别到 {len(texts)} 个文本区域")

                            # 构造标准格式的结果
                            ocr_results = []
                            for i, (text, score, poly) in enumerate(zip(texts, scores, polys)):
                                print(f"    文本{i+1}: '{text}' (置信度: {score:.3f})")
                                ocr_results.append((poly, (text, score)))

                            return ocr_results
                        else:
                            print("  文本、分数、多边形数量不匹配")
                            return []
                    except (KeyError, TypeError):
                        print("  无法访问OCRResult对象的数据")

                # 传统格式处理
                else:
                    print("  无法识别的列表格式")
                    return []

            else:
                print(f"  未知的OCR结果格式: {type(result)}")
                return []

        except Exception as e:
            print(f"  OCR识别失败 {image_path}: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def find_license_plate(self, ocr_results: List[Tuple]) -> Optional[Dict]:
        """
        从OCR结果中查找车牌信息
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            车牌信息字典，包含车牌号、颜色等
        """
        # 车牌号码正则表达式
        license_patterns = [
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}',  # 普通车牌
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]',  # 特殊车牌
        ]
        
        # 颜色关键词
        color_keywords = ['蓝', '黄', '白', '黑', '绿', '红']
        
        for item in ocr_results:
            if len(item) != 2:
                continue
            bbox, (text, confidence) = item
            if confidence < 0.5:  # 置信度过低跳过
                continue
                
            # 查找车牌号码
            for pattern in license_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    license_plate = matches[0]
                    
                    # 查找颜色信息（通常在车牌号附近）
                    color = None
                    for color_word in color_keywords:
                        if color_word in text:
                            color = color_word
                            break
                    
                    return {
                        'license_plate': license_plate,
                        'color': color,
                        'full_text': text,
                        'confidence': confidence,
                        'bbox': bbox
                    }
        
        return None
    
    def find_vehicle_model(self, ocr_results: List[Tuple]) -> Optional[str]:
        """
        查找车型信息
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            车型字符串
        """
        # 常见车型关键词
        vehicle_keywords = [
            '轿车', 'SUV', 'MPV', '面包车', '货车', '客车', '卡车',
            '奔驰', '宝马', '奥迪', '大众', '丰田', '本田', '日产',
            '比亚迪', '吉利', '长安', '哈弗', '红旗', '蔚来', '理想',
            '小鹏', '特斯拉'
        ]
        
        for item in ocr_results:
            if len(item) != 2:
                continue
            bbox, (text, confidence) = item
            if confidence < 0.6:
                continue

            for keyword in vehicle_keywords:
                if keyword in text:
                    return text.strip()
        
        return None
    
    def find_order_count(self, ocr_results: List[Tuple]) -> Optional[int]:
        """
        查找接单次数
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            接单次数
        """
        # 查找数字模式，通常接单次数会有"次"、"单"等关键词
        order_patterns = [
            r'(\d+)\s*次',
            r'(\d+)\s*单',
            r'接单\s*(\d+)',
            r'(\d+)\s*接单'
        ]
        
        for item in ocr_results:
            if len(item) != 2:
                continue
            bbox, (text, confidence) = item
            if confidence < 0.6:
                continue

            for pattern in order_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    try:
                        return int(matches[0])
                    except ValueError:
                        continue

        # 如果没有找到明确的接单次数，查找右侧的数字
        # 根据bbox位置判断是否在右侧
        right_side_numbers = []
        for item in ocr_results:
            if len(item) != 2:
                continue
            bbox, (text, confidence) = item
            if confidence < 0.6:
                continue
                
            # 简单判断是否在图片右侧（x坐标较大）
            x_coords = [point[0] for point in bbox]
            avg_x = sum(x_coords) / len(x_coords)
            
            # 查找纯数字
            numbers = re.findall(r'\d+', text)
            for num in numbers:
                if len(num) <= 4:  # 接单次数通常不会太大
                    right_side_numbers.append((avg_x, int(num)))
        
        # 返回最右侧的数字
        if right_side_numbers:
            right_side_numbers.sort(key=lambda x: x[0], reverse=True)
            return right_side_numbers[0][1]
        
        return None
    
    def get_crop_regions(self, image_path: str) -> List[Tuple]:
        """
        根据图片类型返回需要裁剪的区域

        Args:
            image_path: 图片路径

        Returns:
            裁剪区域列表，每个元素为(x1, y1, x2, y2, region_name)
        """
        # 根据图片名称或路径判断是哪种类型的截图
        filename = Path(image_path).name.lower()

        # 这些坐标需要根据实际图片调整
        if 'screenshot' in filename:
            # 顺风车截图的固定区域
            return [
                (15, 1480, 750, 1620, "车牌区域"),      # 车牌号码和颜色区域
                (15, 1620, 750, 1720, "车型区域"),      # 车型区域
                (700, 1620, 1080, 1720, "接单次数区域")  # 接单次数区域
            ]
        else:
            # 如果不是截图，使用全图识别
            return None

    def process_image(self, image_path: str, use_crop: bool = True) -> Dict:
        """
        处理单张图片，提取所有信息

        Args:
            image_path: 图片路径
            use_crop: 是否使用区域裁剪

        Returns:
            包含所有识别信息的字典
        """
        print(f"正在处理图片: {image_path}")

        # 获取裁剪区域
        crop_regions = self.get_crop_regions(image_path) if use_crop else None

        # OCR识别
        ocr_results = self.extract_text_from_image(image_path, crop_regions)

        if not ocr_results:
            return {
                'image_path': image_path,
                'license_plate_info': None,
                'vehicle_model': None,
                'order_count': None,
                'all_text': []
            }

        # 提取各种信息
        license_plate_info = self.find_license_plate(ocr_results)
        vehicle_model = self.find_vehicle_model(ocr_results)
        order_count = self.find_order_count(ocr_results)

        # 保存所有识别的文本
        all_text = []
        for item in ocr_results:
            if len(item) == 2:
                bbox, (text, confidence) = item
                all_text.append((text, confidence))

        result = {
            'image_path': image_path,
            'license_plate_info': license_plate_info,
            'vehicle_model': vehicle_model,
            'order_count': order_count,
            'all_text': all_text
        }

        return result
    
    def process_directory(self, data_dir: str = 'data') -> List[Dict]:
        """
        处理data目录下的所有图片
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            所有图片的识别结果列表
        """
        results = []
        data_path = Path(data_dir)
        
        if not data_path.exists():
            print(f"数据目录不存在: {data_dir}")
            return results
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 递归查找所有图片文件
        for image_file in data_path.rglob('*'):
            if image_file.suffix.lower() in image_extensions:
                result = self.process_image(str(image_file))
                results.append(result)
        
        return results
    
    def save_results(self, results: List[Dict], output_file: str = 'ocr_results.json'):
        """
        保存识别结果到JSON文件

        Args:
            results: 识别结果列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_file}")

    def save_results_csv(self, results: List[Dict], output_file: str = 'ocr_results.csv'):
        """
        保存识别结果到CSV文件

        Args:
            results: 识别结果列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)

            # 写入表头
            headers = [
                '图片路径', '文件名', '城市', '人员', '平台',
                '车牌号码', '车牌颜色', '车型', '接单次数',
                '所有识别文本', '识别文本数量'
            ]
            writer.writerow(headers)

            # 写入数据
            for result in results:
                # 解析路径信息
                path_parts = Path(result['image_path']).parts
                filename = Path(result['image_path']).name

                city = ''
                person = ''
                platform = ''

                # 尝试从路径中提取城市、人员、平台信息
                if len(path_parts) >= 3:
                    city = path_parts[1] if path_parts[1] != 'data' else ''
                if len(path_parts) >= 5:
                    person = path_parts[3]
                if len(path_parts) >= 6:
                    platform = path_parts[4]

                # 车牌信息
                license_plate = ''
                license_color = ''
                if result['license_plate_info']:
                    license_plate = result['license_plate_info']['license_plate']
                    license_color = result['license_plate_info']['color'] or ''

                # 车型
                vehicle_model = result['vehicle_model'] or ''

                # 接单次数
                order_count = result['order_count'] if result['order_count'] is not None else ''

                # 所有识别文本
                all_texts = []
                for text, confidence in result['all_text']:
                    all_texts.append(f"{text}({confidence:.2f})")
                all_text_str = '; '.join(all_texts)

                # 识别文本数量
                text_count = len(result['all_text'])

                row = [
                    result['image_path'], filename, city, person, platform,
                    license_plate, license_color, vehicle_model, order_count,
                    all_text_str, text_count
                ]
                writer.writerow(row)

        print(f"CSV结果已保存到: {output_file}")
    
    def print_summary(self, results: List[Dict]):
        """
        打印识别结果摘要
        
        Args:
            results: 识别结果列表
        """
        print("\n" + "="*50)
        print("识别结果摘要")
        print("="*50)
        
        total_images = len(results)
        license_found = sum(1 for r in results if r['license_plate_info'])
        vehicle_found = sum(1 for r in results if r['vehicle_model'])
        order_found = sum(1 for r in results if r['order_count'] is not None)
        
        print(f"总图片数: {total_images}")
        print(f"识别到车牌: {license_found}")
        print(f"识别到车型: {vehicle_found}")
        print(f"识别到接单次数: {order_found}")
        print()
        
        for result in results:
            print(f"图片: {Path(result['image_path']).name}")
            
            if result['license_plate_info']:
                info = result['license_plate_info']
                print(f"  车牌: {info['license_plate']}")
                if info['color']:
                    print(f"  颜色: {info['color']}")
            
            if result['vehicle_model']:
                print(f"  车型: {result['vehicle_model']}")
            
            if result['order_count'] is not None:
                print(f"  接单次数: {result['order_count']}")
            
            print()


def main():
    """主函数"""
    print("开始车牌识别...")

    # 初始化OCR
    ocr_processor = LicensePlateOCR()

    # 处理所有图片
    results = ocr_processor.process_directory('data')

    # 打印摘要
    ocr_processor.print_summary(results)

    # 保存结果
    ocr_processor.save_results(results)
    ocr_processor.save_results_csv(results)

    print("处理完成！")


if __name__ == "__main__":
    main()
