#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别脚本
使用PaddleOCR识别图片中的车牌号码、颜色、车型和接单次数
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont


class LicensePlateOCR:
    def __init__(self, use_angle_cls=True, lang='ch'):
        """
        初始化PaddleOCR
        
        Args:
            use_angle_cls: 是否使用角度分类器
            lang: 语言，默认中文
        """
        self.ocr = PaddleOCR(use_angle_cls=use_angle_cls, lang=lang)
        
    def extract_text_from_image(self, image_path: str) -> List[Tuple]:
        """
        从图片中提取文本
        
        Args:
            image_path: 图片路径
            
        Returns:
            包含文本信息的列表，每个元素为(坐标, 文本, 置信度)
        """
        try:
            result = self.ocr.ocr(image_path, cls=True)
            if result and result[0]:
                return result[0]
            return []
        except Exception as e:
            print(f"OCR识别失败 {image_path}: {e}")
            return []
    
    def find_license_plate(self, ocr_results: List[Tuple]) -> Optional[Dict]:
        """
        从OCR结果中查找车牌信息
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            车牌信息字典，包含车牌号、颜色等
        """
        # 车牌号码正则表达式
        license_patterns = [
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}',  # 普通车牌
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]',  # 特殊车牌
        ]
        
        # 颜色关键词
        color_keywords = ['蓝', '黄', '白', '黑', '绿', '红']
        
        for bbox, (text, confidence) in ocr_results:
            if confidence < 0.5:  # 置信度过低跳过
                continue
                
            # 查找车牌号码
            for pattern in license_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    license_plate = matches[0]
                    
                    # 查找颜色信息（通常在车牌号附近）
                    color = None
                    for color_word in color_keywords:
                        if color_word in text:
                            color = color_word
                            break
                    
                    return {
                        'license_plate': license_plate,
                        'color': color,
                        'full_text': text,
                        'confidence': confidence,
                        'bbox': bbox
                    }
        
        return None
    
    def find_vehicle_model(self, ocr_results: List[Tuple]) -> Optional[str]:
        """
        查找车型信息
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            车型字符串
        """
        # 常见车型关键词
        vehicle_keywords = [
            '轿车', 'SUV', 'MPV', '面包车', '货车', '客车', '卡车',
            '奔驰', '宝马', '奥迪', '大众', '丰田', '本田', '日产',
            '比亚迪', '吉利', '长安', '哈弗', '红旗', '蔚来', '理想',
            '小鹏', '特斯拉'
        ]
        
        for bbox, (text, confidence) in ocr_results:
            if confidence < 0.6:
                continue
                
            for keyword in vehicle_keywords:
                if keyword in text:
                    return text.strip()
        
        return None
    
    def find_order_count(self, ocr_results: List[Tuple]) -> Optional[int]:
        """
        查找接单次数
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            接单次数
        """
        # 查找数字模式，通常接单次数会有"次"、"单"等关键词
        order_patterns = [
            r'(\d+)\s*次',
            r'(\d+)\s*单',
            r'接单\s*(\d+)',
            r'(\d+)\s*接单'
        ]
        
        for bbox, (text, confidence) in ocr_results:
            if confidence < 0.6:
                continue
                
            for pattern in order_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    try:
                        return int(matches[0])
                    except ValueError:
                        continue
        
        # 如果没有找到明确的接单次数，查找右侧的数字
        # 根据bbox位置判断是否在右侧
        right_side_numbers = []
        for bbox, (text, confidence) in ocr_results:
            if confidence < 0.6:
                continue
                
            # 简单判断是否在图片右侧（x坐标较大）
            x_coords = [point[0] for point in bbox]
            avg_x = sum(x_coords) / len(x_coords)
            
            # 查找纯数字
            numbers = re.findall(r'\d+', text)
            for num in numbers:
                if len(num) <= 4:  # 接单次数通常不会太大
                    right_side_numbers.append((avg_x, int(num)))
        
        # 返回最右侧的数字
        if right_side_numbers:
            right_side_numbers.sort(key=lambda x: x[0], reverse=True)
            return right_side_numbers[0][1]
        
        return None
    
    def process_image(self, image_path: str) -> Dict:
        """
        处理单张图片，提取所有信息
        
        Args:
            image_path: 图片路径
            
        Returns:
            包含所有识别信息的字典
        """
        print(f"正在处理图片: {image_path}")
        
        # OCR识别
        ocr_results = self.extract_text_from_image(image_path)
        
        if not ocr_results:
            return {
                'image_path': image_path,
                'license_plate_info': None,
                'vehicle_model': None,
                'order_count': None,
                'all_text': []
            }
        
        # 提取各种信息
        license_plate_info = self.find_license_plate(ocr_results)
        vehicle_model = self.find_vehicle_model(ocr_results)
        order_count = self.find_order_count(ocr_results)
        
        # 保存所有识别的文本
        all_text = [(text, confidence) for bbox, (text, confidence) in ocr_results]
        
        result = {
            'image_path': image_path,
            'license_plate_info': license_plate_info,
            'vehicle_model': vehicle_model,
            'order_count': order_count,
            'all_text': all_text
        }
        
        return result
    
    def process_directory(self, data_dir: str = 'data') -> List[Dict]:
        """
        处理data目录下的所有图片
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            所有图片的识别结果列表
        """
        results = []
        data_path = Path(data_dir)
        
        if not data_path.exists():
            print(f"数据目录不存在: {data_dir}")
            return results
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 递归查找所有图片文件
        for image_file in data_path.rglob('*'):
            if image_file.suffix.lower() in image_extensions:
                result = self.process_image(str(image_file))
                results.append(result)
        
        return results
    
    def save_results(self, results: List[Dict], output_file: str = 'ocr_results.json'):
        """
        保存识别结果到JSON文件
        
        Args:
            results: 识别结果列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_file}")
    
    def print_summary(self, results: List[Dict]):
        """
        打印识别结果摘要
        
        Args:
            results: 识别结果列表
        """
        print("\n" + "="*50)
        print("识别结果摘要")
        print("="*50)
        
        total_images = len(results)
        license_found = sum(1 for r in results if r['license_plate_info'])
        vehicle_found = sum(1 for r in results if r['vehicle_model'])
        order_found = sum(1 for r in results if r['order_count'] is not None)
        
        print(f"总图片数: {total_images}")
        print(f"识别到车牌: {license_found}")
        print(f"识别到车型: {vehicle_found}")
        print(f"识别到接单次数: {order_found}")
        print()
        
        for result in results:
            print(f"图片: {Path(result['image_path']).name}")
            
            if result['license_plate_info']:
                info = result['license_plate_info']
                print(f"  车牌: {info['license_plate']}")
                if info['color']:
                    print(f"  颜色: {info['color']}")
            
            if result['vehicle_model']:
                print(f"  车型: {result['vehicle_model']}")
            
            if result['order_count'] is not None:
                print(f"  接单次数: {result['order_count']}")
            
            print()


def main():
    """主函数"""
    print("开始车牌识别...")
    
    # 初始化OCR
    ocr_processor = LicensePlateOCR()
    
    # 处理所有图片
    results = ocr_processor.process_directory('data')
    
    # 打印摘要
    ocr_processor.print_summary(results)
    
    # 保存结果
    ocr_processor.save_results(results)
    
    print("处理完成！")


if __name__ == "__main__":
    main()
